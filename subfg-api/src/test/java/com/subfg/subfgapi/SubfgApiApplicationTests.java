package com.subfg.subfgapi;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import com.subfg.subfgapi.Serivce.EmailService;

@SpringBootTest
class SubfgApiApplicationTests {


    @Autowired
    private EmailService emailService;
    @Test
    void contextLoads() {
    }


    @Test
    void test1(){
        emailService.sendVerifyCodeEmail("<EMAIL>","123456",1);
    }


}
