package com.subfg.domain.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;



@Data
@Schema(description = "创建家庭组请求")
public class CreateFamilyGroupReq {

    /**
     * 家庭组名称
     */
    @Schema(description = "家庭组名称")
    private String name;

    /**
     * 产品ID
     */
    @Schema(description = "产品ID")
    private Integer productId;

    /**
     * 分类ID
     */
    @Schema(description = "分类ID")
    private String categoryId;

    /**
     * 描述
     */
    @Schema(description = "家庭组描述")
    private String description;

}