package com.subfg.domain.entity.fg;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 家庭组成员实体类
 * 对应数据库表：fg_member
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("fg_member")
public class FgMemberPo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 成员ID（主键）
     */
    @TableId("member_id")
    private String memberId;

    /**
     * 家庭组ID
     */
    @TableField("family_group_id")
    private String familyGroupId;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 家庭组所有者
     */
    @TableField("family_group_owner")
    private String familyGroupOwner;

    /**
     * 计费周期
     */
    @TableField("billing_cycle")
    private Integer billingCycle;

    /**
     * 邀请链接
     */
    @TableField("invite_url")
    private String inviteUrl;

    /**
     * 旧账号
     */
    @TableField("old_account")
    private String oldAccount;

    /**
     * 第三方账号
     */
    @TableField("third_account")
    private String thirdAccount;

    /**
     * 状态
     */
    @TableField("status")
    private Integer status;

    /**
     * 邀请时间
     */
    @TableField("invitation_time")
    private Long invitationTime;

    /**
     * 确认时间
     */
    @TableField("confirm_time")
    private Long confirmTime;

    /**
     * 服务开始时间
     */
    @TableField("service_start_time")
    private Long serviceStartTime;

    /**
     * 服务结束时间
     */
    @TableField("service_over_time")
    private Long serviceOverTime;

    /**
     * 服务过期时间
     */
    @TableField("service_expire_time")
    private Long serviceExpireTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Long updateTime;

    /**
     * 提示次数
     */
    @TableField("hint_count")
    private Integer hintCount;

    /**
     * 提示时间
     */
    @TableField("hint_time")
    private Long hintTime;

    /**
     * 证明图片
     */
    @TableField("proof_images")
    private String proofImages;
}
