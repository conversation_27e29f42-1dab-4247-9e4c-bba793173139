package com.subfg.domain.entity.fg;

import java.io.Serializable;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 家庭组自定义订阅实体类
 * 对应数据库表：fg_substore_custom
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("fg_substore_custom")
public class FgSubstoreCustomPo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 家庭组ID（主键）
     */
    @TableId("family_group_id")
    private String familyGroupId;

    /**
     * 家庭组名称
     */
    @TableField("family_group_name")
    private String familyGroupName;

    /**
     * 产品ID
     */
    @TableField("product_id")
    private String productId;

    /**
     * 分类ID
     */
    @TableField("category_id")
    private String categoryId;

    /**
     * 周期
     */
    @TableField("cycle")
    private Integer cycle;

    /**
     * 创建用户
     */
    @TableField("create_user")
    private String createUser;

    /**
     * 套餐ID
     */
    @TableField("plan_id")
    private String planId;

    /**
     * 家庭组状态
     */
    @TableField("family_group_status")
    private Integer familyGroupStatus;

    /**
     * 计费周期
     */
    @TableField("billing_cycle")
    private Integer billingCycle;

    /**
     * 地区ID
     */
    @TableField("region_id")
    private Integer regionId;

    /**
     * 金额
     */
    @TableField("amount")
    private BigDecimal amount;

    /**
     * 描述
     */
    @TableField("description")
    private String description;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Long createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Long updateTime;

    /**
     * 服务过期时间
     */
    @TableField("service_expire_time")
    private Long serviceExpireTime;

    /**
     * 第三方详情
     */
    @TableField("third_detail")
    private String thirdDetail;

    /**
     * 支付时间
     */
    @TableField("pay_time")
    private String payTime;

    /**
     * 是否新产品
     */
    @TableField("is_new_product")
    private Boolean isNewProduct;

    /**
     * 支付方式
     */
    @TableField("pay_method")
    private String payMethod;

    /**
     * 货币
     */
    @TableField("currency")
    private String currency;

    /**
     * 免费天数
     */
    @TableField("free_day")
    private Integer freeDay;
}
