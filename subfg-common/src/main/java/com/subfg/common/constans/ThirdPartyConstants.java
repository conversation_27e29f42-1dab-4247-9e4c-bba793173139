package com.subfg.common.constans;

/**
 * 三方登录相关常量
 * 
 * <AUTHOR>
 * @since 2025-01-31
 */
public class ThirdPartyConstants {

    /**
     * 三方登录状态
     */
    public static class Status {
        /** 正常 */
        public static final int NORMAL = 1;
        /** 禁用 */
        public static final int DISABLED = 0;
    }

    /**
     * 三方平台配置键名
     */
    public static class ConfigKey {
        /** 微信应用ID */
        public static final String WECHAT_APP_ID = "wx1880d44d41318723";
        /** 微信应用密钥 */
        public static final String WECHAT_APP_SECRET = "b4adefa2233dc9e41074ccb3e6eb3c04";
        
        /** QQ应用ID */
        public static final String QQ_APP_ID = "qq.app.id";
        /** QQ应用密钥 */
        public static final String QQ_APP_SECRET = "qq.app.secret";
        /** QQ回调地址 */
        public static final String QQ_REDIRECT_URI = "qq.redirect.uri";

        /** 谷歌客户端ID */
        public static final String GOOGLE_CLIENT_ID = "google.client.id";
        /** 谷歌客户端密钥 */
        public static final String GOOGLE_CLIENT_SECRET = "google.client.secret";
        /** 谷歌回调地址 */
        public static final String GOOGLE_REDIRECT_URI = "google.redirect.uri";

        /** GitHub客户端ID */
        public static final String GITHUB_CLIENT_ID = "github.client.id";
        /** GitHub客户端密钥 */
        public static final String GITHUB_CLIENT_SECRET = "github.client.secret";
        /** GitHub回调地址 */
        public static final String GITHUB_REDIRECT_URI = "github.redirect.uri";
    }

    /**
     * 三方登录API地址
     */
    public static class ApiUrl {
        /** 微信授权地址 */
        public static final String WECHAT_AUTH_URL = "https://open.weixin.qq.com/connect/qrconnect";
        /** 微信获取access_token地址 */
        public static final String WECHAT_TOKEN_URL = "https://api.weixin.qq.com/sns/oauth2/access_token";
        /** 微信获取用户信息地址 */
        public static final String WECHAT_USER_INFO_URL = "https://api.weixin.qq.com/sns/userinfo";

        /** QQ授权地址 */
        public static final String QQ_AUTH_URL = "https://graph.qq.com/oauth2.0/authorize";
        /** QQ获取access_token地址 */
        public static final String QQ_TOKEN_URL = "https://graph.qq.com/oauth2.0/token";
        /** QQ获取用户信息地址 */
        public static final String QQ_USER_INFO_URL = "https://graph.qq.com/user/get_user_info";

        /** 谷歌授权地址 */
        public static final String GOOGLE_AUTH_URL = "https://accounts.google.com/o/oauth2/v2/auth";
        /** 谷歌获取access_token地址 */
        public static final String GOOGLE_TOKEN_URL = "https://oauth2.googleapis.com/token";
        /** 谷歌获取用户信息地址 */
        public static final String GOOGLE_USER_INFO_URL = "https://www.googleapis.com/oauth2/v2/userinfo";

        /** GitHub授权地址 */
        public static final String GITHUB_AUTH_URL = "https://github.com/login/oauth/authorize";
        /** GitHub获取access_token地址 */
        public static final String GITHUB_TOKEN_URL = "https://github.com/login/oauth/access_token";
        /** GitHub获取用户信息地址 */
        public static final String GITHUB_USER_INFO_URL = "https://api.github.com/user";
    }

}
