package com.subfg.common.service;

import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

import org.springframework.amqp.core.AmqpAdmin;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.QueueInformation;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * RabbitMQ管理服务
 * 提供队列管理、监控等功能
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RabbitMQManagementService {

    private final RabbitTemplate rabbitTemplate;
    private final AmqpAdmin amqpAdmin;

    /**
     * 获取队列信息
     *
     * @param queueName 队列名称
     * @return 队列信息
     */
    public QueueInfo getQueueInfo(String queueName) {
        try {
            Properties queueProperties = amqpAdmin.getQueueProperties(queueName);
            if (queueProperties == null) {
                log.warn("队列不存在: {}", queueName);
                return null;
            }

            QueueInformation queueInfo = amqpAdmin.getQueueInfo(queueName);
            
            return QueueInfo.builder()
                    .name(queueName)
                    .messageCount(queueInfo != null ? queueInfo.getMessageCount() : 0)
                    .consumerCount(queueInfo != null ? queueInfo.getConsumerCount() : 0)
                    .properties(queueProperties)
                    .build();
                    
        } catch (Exception e) {
            log.error("获取队列信息失败: {}", queueName, e);
            return null;
        }
    }

    /**
     * 创建队列
     *
     * @param queueName 队列名称
     * @param durable   是否持久化
     * @param exclusive 是否排他
     * @param autoDelete 是否自动删除
     * @param arguments 队列参数
     * @return 是否创建成功
     */
    public boolean createQueue(String queueName, boolean durable, boolean exclusive, 
                              boolean autoDelete, Map<String, Object> arguments) {
        try {
            Queue queue = new Queue(queueName, durable, exclusive, autoDelete, arguments);
            amqpAdmin.declareQueue(queue);
            log.info("队列创建成功: {}", queueName);
            return true;
        } catch (Exception e) {
            log.error("队列创建失败: {}", queueName, e);
            return false;
        }
    }

    /**
     * 删除队列
     *
     * @param queueName 队列名称
     * @param unused    是否只删除未使用的队列
     * @param empty     是否只删除空队列
     * @return 是否删除成功
     */
    public boolean deleteQueue(String queueName, boolean unused, boolean empty) {
        try {
            boolean result = amqpAdmin.deleteQueue(queueName, unused, empty);
            if (result) {
                log.info("队列删除成功: {}", queueName);
            } else {
                log.warn("队列删除失败或队列不存在: {}", queueName);
            }
            return result;
        } catch (Exception e) {
            log.error("队列删除失败: {}", queueName, e);
            return false;
        }
    }

    /**
     * 清空队列
     *
     * @param queueName 队列名称
     * @return 是否清空成功
     */
    public boolean purgeQueue(String queueName) {
        try {
            amqpAdmin.purgeQueue(queueName);
            log.info("队列清空成功: {}", queueName);
            return true;
        } catch (Exception e) {
            log.error("队列清空失败: {}", queueName, e);
            return false;
        }
    }

    /**
     * 获取所有队列的统计信息
     *
     * @return 队列统计信息
     */
    public Map<String, QueueInfo> getAllQueuesInfo() {
        Map<String, QueueInfo> queuesInfo = new HashMap<>();
        
        // 预定义的队列列表
        String[] queueNames = {
            "subfg.user.notification.queue",
            "subfg.email.queue",
            "subfg.dead.letter.queue"
        };
        
        for (String queueName : queueNames) {
            QueueInfo queueInfo = getQueueInfo(queueName);
            if (queueInfo != null) {
                queuesInfo.put(queueName, queueInfo);
            }
        }
        
        return queuesInfo;
    }

    /**
     * 检查RabbitMQ连接状态
     *
     * @return 连接是否正常
     */
    public boolean checkConnection() {
        try {
            // 尝试获取一个队列的信息来测试连接
            amqpAdmin.getQueueProperties("test.connection.queue");
            return true;
        } catch (Exception e) {
            log.error("RabbitMQ连接检查失败", e);
            return false;
        }
    }

    /**
     * 获取RabbitMQ健康状态
     *
     * @return 健康状态信息
     */
    public HealthStatus getHealthStatus() {
        try {
            boolean connectionOk = checkConnection();
            Map<String, QueueInfo> queuesInfo = getAllQueuesInfo();
            
            int totalMessages = queuesInfo.values().stream()
                    .mapToInt(QueueInfo::getMessageCount)
                    .sum();
                    
            int totalConsumers = queuesInfo.values().stream()
                    .mapToInt(QueueInfo::getConsumerCount)
                    .sum();
            
            return HealthStatus.builder()
                    .connectionOk(connectionOk)
                    .totalQueues(queuesInfo.size())
                    .totalMessages(totalMessages)
                    .totalConsumers(totalConsumers)
                    .queuesInfo(queuesInfo)
                    .timestamp(System.currentTimeMillis())
                    .build();
                    
        } catch (Exception e) {
            log.error("获取健康状态失败", e);
            return HealthStatus.builder()
                    .connectionOk(false)
                    .totalQueues(0)
                    .totalMessages(0)
                    .totalConsumers(0)
                    .timestamp(System.currentTimeMillis())
                    .build();
        }
    }

    /**
     * 发送测试消息
     *
     * @param exchange   交换机
     * @param routingKey 路由键
     * @param message    消息内容
     * @return 是否发送成功
     */
    public boolean sendTestMessage(String exchange, String routingKey, String message) {
        try {
            rabbitTemplate.convertAndSend(exchange, routingKey, message);
            log.info("测试消息发送成功 - exchange: {}, routingKey: {}", exchange, routingKey);
            return true;
        } catch (Exception e) {
            log.error("测试消息发送失败 - exchange: {}, routingKey: {}", exchange, routingKey, e);
            return false;
        }
    }

    // ==================== 内部数据类 ====================

    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class QueueInfo {
        private String name;
        private int messageCount;
        private int consumerCount;
        private Properties properties;
    }

    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class HealthStatus {
        private boolean connectionOk;
        private int totalQueues;
        private int totalMessages;
        private int totalConsumers;
        private Map<String, QueueInfo> queuesInfo;
        private long timestamp;
    }
}
