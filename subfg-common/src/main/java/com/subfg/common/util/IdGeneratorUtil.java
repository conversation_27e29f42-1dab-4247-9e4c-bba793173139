package com.subfg.common.util;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.ConcurrentHashMap;

/**
 * 业务ID生成工具类
 * 基于Hutool雪花算法生成带业务前缀的唯一ID
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
public class IdGeneratorUtil {

    /**
     * 默认数据中心ID
     */
    private static final long DEFAULT_DATACENTER_ID = 1L;
    
    /**
     * 默认机器ID
     */
    private static final long DEFAULT_WORKER_ID = 1L;
    
    /**
     * 雪花算法实例缓存
     */
    private static final ConcurrentHashMap<String, Snowflake> SNOWFLAKE_CACHE = new ConcurrentHashMap<>();
    
    /**
     * 默认雪花算法实例
     */
    private static final Snowflake DEFAULT_SNOWFLAKE = IdUtil.getSnowflake(DEFAULT_WORKER_ID, DEFAULT_DATACENTER_ID);

    /**
     * 业务前缀枚举
     */
    public enum BusinessPrefix {
        /**
         * 家庭组ID前缀
         */
        FAMILY_GROUP("FG"),
        
        /**
         * 用户ID前缀
         */
        USER("U"),
        
        /**
         * 订单ID前缀
         */
        ORDER("ORD"),
        
        /**
         * 产品ID前缀
         */
        PRODUCT("PRD"),
        
        /**
         * 套餐ID前缀
         */
        PLAN("PLN"),
        
        /**
         * 支付ID前缀
         */
        PAYMENT("PAY"),
        
        /**
         * 审核ID前缀
         */
        REVIEW("REV"),
        
        /**
         * 通知ID前缀
         */
        NOTIFICATION("NTF"),
        
        /**
         * 邮件配置ID前缀
         */
        EMAIL_CONFIG("EMC"),
        
        /**
         * 日志ID前缀
         */
        LOG("LOG");
        
        private final String prefix;
        
        BusinessPrefix(String prefix) {
            this.prefix = prefix;
        }
        
        public String getPrefix() {
            return prefix;
        }
    }

    /**
     * 生成带业务前缀的ID
     *
     * @param businessPrefix 业务前缀枚举
     * @return 生成的业务ID，格式：前缀 + 雪花ID
     */
    public static String generateId(BusinessPrefix businessPrefix) {
        return businessPrefix.getPrefix() + DEFAULT_SNOWFLAKE.nextId();
    }

    /**
     * 生成带自定义前缀的ID
     *
     * @param prefix 自定义前缀
     * @return 生成的业务ID，格式：前缀 + 雪花ID
     */
    public static String generateId(String prefix) {
        if (prefix == null || prefix.trim().isEmpty()) {
            throw new IllegalArgumentException("前缀不能为空");
        }
        return prefix + DEFAULT_SNOWFLAKE.nextId();
    }

    /**
     * 生成纯雪花ID（不带前缀）
     *
     * @return 雪花ID
     */
    public static long generateSnowflakeId() {
        return DEFAULT_SNOWFLAKE.nextId();
    }

    /**
     * 生成纯雪花ID字符串（不带前缀）
     *
     * @return 雪花ID字符串
     */
    public static String generateSnowflakeIdStr() {
        return String.valueOf(DEFAULT_SNOWFLAKE.nextId());
    }

    /**
     * 使用指定的数据中心ID和机器ID生成雪花ID
     *
     * @param workerId     机器ID (0-31)
     * @param datacenterId 数据中心ID (0-31)
     * @param prefix       业务前缀
     * @return 生成的业务ID
     */
    public static String generateId(long workerId, long datacenterId, String prefix) {
        String key = workerId + "_" + datacenterId;
        Snowflake snowflake = SNOWFLAKE_CACHE.computeIfAbsent(key, 
            k -> IdUtil.getSnowflake(workerId, datacenterId));
        
        if (prefix == null || prefix.trim().isEmpty()) {
            return String.valueOf(snowflake.nextId());
        }
        return prefix + snowflake.nextId();
    }

    /**
     * 使用指定的数据中心ID和机器ID生成雪花ID
     *
     * @param workerId       机器ID (0-31)
     * @param datacenterId   数据中心ID (0-31)
     * @param businessPrefix 业务前缀枚举
     * @return 生成的业务ID
     */
    public static String generateId(long workerId, long datacenterId, BusinessPrefix businessPrefix) {
        return generateId(workerId, datacenterId, businessPrefix.getPrefix());
    }

    // ==================== 便捷方法 ====================

    /**
     * 生成家庭组ID
     *
     * @return 家庭组ID，格式：FG + 雪花ID
     */
    public static String generateFamilyGroupId() {
        return generateId(BusinessPrefix.FAMILY_GROUP);
    }

    /**
     * 生成用户ID
     *
     * @return 用户ID，格式：U + 雪花ID
     */
    public static String generateUserId() {
        return generateId(BusinessPrefix.USER);
    }

    /**
     * 生成订单ID
     *
     * @return 订单ID，格式：ORD + 雪花ID
     */
    public static String generateOrderId() {
        return generateId(BusinessPrefix.ORDER);
    }

    /**
     * 生成产品ID
     *
     * @return 产品ID，格式：PRD + 雪花ID
     */
    public static String generateProductId() {
        return generateId(BusinessPrefix.PRODUCT);
    }

    /**
     * 生成套餐ID
     *
     * @return 套餐ID，格式：PLN + 雪花ID
     */
    public static String generatePlanId() {
        return generateId(BusinessPrefix.PLAN);
    }

    /**
     * 生成支付ID
     *
     * @return 支付ID，格式：PAY + 雪花ID
     */
    public static String generatePaymentId() {
        return generateId(BusinessPrefix.PAYMENT);
    }

    /**
     * 生成审核ID
     *
     * @return 审核ID，格式：REV + 雪花ID
     */
    public static String generateReviewId() {
        return generateId(BusinessPrefix.REVIEW);
    }

    /**
     * 生成通知ID
     *
     * @return 通知ID，格式：NTF + 雪花ID
     */
    public static String generateNotificationId() {
        return generateId(BusinessPrefix.NOTIFICATION);
    }

    /**
     * 生成邮件配置ID
     *
     * @return 邮件配置ID，格式：EMC + 雪花ID
     */
    public static String generateEmailConfigId() {
        return generateId(BusinessPrefix.EMAIL_CONFIG);
    }

    /**
     * 生成日志ID
     *
     * @return 日志ID，格式：LOG + 雪花ID
     */
    public static String generateLogId() {
        return generateId(BusinessPrefix.LOG);
    }

    /**
     * 验证ID格式是否正确
     *
     * @param id             待验证的ID
     * @param businessPrefix 期望的业务前缀
     * @return true-格式正确，false-格式错误
     */
    public static boolean validateIdFormat(String id, BusinessPrefix businessPrefix) {
        if (id == null || id.trim().isEmpty()) {
            return false;
        }
        
        String prefix = businessPrefix.getPrefix();
        if (!id.startsWith(prefix)) {
            return false;
        }
        
        String snowflakeIdStr = id.substring(prefix.length());
        try {
            Long.parseLong(snowflakeIdStr);
            return true;
        } catch (NumberFormatException e) {
            log.warn("ID格式验证失败，无效的雪花ID部分: {}", snowflakeIdStr);
            return false;
        }
    }

    /**
     * 从业务ID中提取雪花ID
     *
     * @param businessId     业务ID
     * @param businessPrefix 业务前缀
     * @return 雪花ID，如果格式不正确返回null
     */
    public static Long extractSnowflakeId(String businessId, BusinessPrefix businessPrefix) {
        if (!validateIdFormat(businessId, businessPrefix)) {
            return null;
        }
        
        String prefix = businessPrefix.getPrefix();
        String snowflakeIdStr = businessId.substring(prefix.length());
        try {
            return Long.parseLong(snowflakeIdStr);
        } catch (NumberFormatException e) {
            log.warn("提取雪花ID失败: {}", businessId);
            return null;
        }
    }

    /**
     * 清理雪花算法实例缓存
     */
    public static void clearCache() {
        SNOWFLAKE_CACHE.clear();
        log.info("雪花算法实例缓存已清理");
    }

    /**
     * 获取缓存中的雪花算法实例数量
     *
     * @return 缓存实例数量
     */
    public static int getCacheSize() {
        return SNOWFLAKE_CACHE.size();
    }
}
