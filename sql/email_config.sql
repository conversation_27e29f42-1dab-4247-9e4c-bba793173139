-- 邮箱配置表
CREATE TABLE IF NOT EXISTS `config_email` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `host` varchar(255) NOT NULL COMMENT 'SMTP服务器地址',
    `account` text NOT NULL COMMENT '邮箱账号（AES加密）',
    `password` text NOT NULL COMMENT '邮箱密码（AES加密）',
    `port` int(11) NOT NULL DEFAULT '587' COMMENT 'SMTP端口',
    `protocol` varchar(50) NOT NULL DEFAULT 'smtp' COMMENT '邮件协议',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='邮箱配置表';

-- 插入示例数据（需要将账号和密码替换为实际的AES加密值）
-- 注意：这里的账号和密码需要使用 AesUtil.aesEncrypt() 方法进行加密后再插入
INSERT INTO `config_email` (`host`, `account`, `password`, `port`, `protocol`) VALUES 
('smtp.qq.com', 'ENCRYPTED_ACCOUNT_HERE', 'ENCRYPTED_PASSWORD_HERE', 587, 'smtp');

-- 查询示例
-- SELECT * FROM config_email;
